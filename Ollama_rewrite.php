<?php
error_reporting(E_ERROR | E_WARNING | E_PARSE);

/**
 * 将Markdown格式转换为HTML格式
 *
 * @param string $content 包含Markdown格式的内容
 * @return string 转换后的HTML格式内容
 */
function convertMarkdownToHTML($content) {
    // 转换代码块：```代码``` -> <pre><code>代码</code></pre>
    $content = preg_replace('/```(.*?)```/su', '<pre><code>$1</code></pre>', $content);
    
    // 转换标题：# 到 ######
    $content = preg_replace('/^######\s+(.*?)$/mu', '<h6>$1</h6>', $content);
    $content = preg_replace('/^#####\s+(.*?)$/mu', '<h5>$1</h5>', $content);
    $content = preg_replace('/^####\s+(.*?)$/mu', '<h4>$1</h4>', $content);
    $content = preg_replace('/^###\s+(.*?)$/mu', '<h3>$1</h3>', $content);
    $content = preg_replace('/^##\s+(.*?)$/mu', '<h2>$1</h2>', $content);
    $content = preg_replace('/^#\s+(.*?)$/mu', '<h1>$1</h1>', $content);
    
    // 转换水平线：--- 或 *** -> <hr>
    $content = preg_replace('/^---+$/mu', '<hr>', $content);
    $content = preg_replace('/^\*\*\*+$/mu', '<hr>', $content);
    
    // 转换无序列表：- 或 * 或 + 开头 -> <ul><li>
    $content = preg_replace('/^[\-\*\+]\s+(.*?)$/mu', '<li>$1</li>', $content);
    $content = preg_replace('/(<li>.*?<\/li>)/su', '<ul>$1</ul>', $content);
    
    // 转换有序列表：1. 开头 -> <ol><li>
    $content = preg_replace('/^\d+\.\s+(.*?)$/mu', '<li>$1</li>', $content);
    // 注意：这里需要更复杂的处理来区分有序和无序列表，简化处理
    
    // 转换表格（简化版）：| 列1 | 列2 | -> <table>
    $content = preg_replace_callback('/^\|(.+)\|$/mu', function($matches) {
        $cells = explode('|', trim($matches[1], '|'));
        $html = '<tr>';
        foreach ($cells as $cell) {
            $html .= '<td>' . trim($cell) . '</td>';
        }
        $html .= '</tr>';
        return $html;
    }, $content);
    $content = preg_replace('/(<tr>.*?<\/tr>)/su', '<table>$1</table>', $content);
    
    // 转换任务列表：- [ ] 或 - [x] -> <input type="checkbox">
    $content = preg_replace('/^\- \[ \]\s+(.*?)$/mu', '<input type="checkbox" disabled> $1', $content);
    $content = preg_replace('/^\- \[x\]\s+(.*?)$/mu', '<input type="checkbox" checked disabled> $1', $content);
    
    // 转换引用：> 文字 -> <blockquote>文字</blockquote>
    $content = preg_replace('/^> (.*?)$/mu', '<blockquote>$1</blockquote>', $content);
    
    // 转换粗体+斜体：***文字*** -> <strong><em>文字</em></strong>
    $content = preg_replace('/\*\*\*(.*?)\*\*\*/u', '<strong><em>$1</em></strong>', $content);
    $content = preg_replace('/\_\_\_(.*?)\_\_\_/u', '<strong><em>$1</em></strong>', $content);
    
    // 转换粗体：**文字** 或 __文字__ -> <strong>文字</strong>
    $content = preg_replace('/\*\*(.*?)\*\*/u', '<strong>$1</strong>', $content);
    $content = preg_replace('/__(.*?)__/u', '<strong>$1</strong>', $content);
    
    // 转换斜体：*文字* 或 _文字_ -> <em>文字</em>
    $content = preg_replace('/\*(.*?)\*/u', '<em>$1</em>', $content);
    $content = preg_replace('/_(.*?)_/u', '<em>$1</em>', $content);
    
    // 转换删除线：~~文字~~ -> <del>文字</del>
    $content = preg_replace('/~~(.*?)~~/u', '<del>$1</del>', $content);
    
    // 转换高亮：==文字== -> <mark>文字</mark>
    $content = preg_replace('/==(.*?)==/u', '<mark>$1</mark>', $content);
    
    // 转换上标：^文字^ -> <sup>文字</sup>
    $content = preg_replace('/\^(.*?)\^/u', '<sup>$1</sup>', $content);
    
    // 转换下标：~文字~ -> <sub>文字</sub>
    $content = preg_replace('/~(.*?)~/u', '<sub>$1</sub>', $content);
    
    // 转换行内代码：`代码` -> <code>代码</code>
    $content = preg_replace('/`(.*?)`/u', '<code>$1</code>', $content);
    
    // 转换键盘按键：[[按键]] -> <kbd>按键</kbd>
    $content = preg_replace('/\[\[(.*?)\]\]/u', '<kbd>$1</kbd>', $content);
    
    // 转换链接：[文字](链接) -> <a href="链接">文字</a>
    $content = preg_replace('/\[(.*?)\]\((.*?)\)/u', '<a href="$2">$1</a>', $content);
    
    // 转换图片：![alt](链接) -> <img src="链接" alt="alt">
    $content = preg_replace('/!\[(.*?)\]\((.*?)\)/u', '<img src="$2" alt="$1">', $content);
    
    // 转换脚注：[^1] -> <sup><a href="#fn1">1</a></sup>
    $content = preg_replace('/\[\^(\d+)\]/u', '<sup><a href="#fn$1">$1</a></sup>', $content);
    
    // 转换换行：两个空格+换行 -> <br>
    $content = preg_replace('/  \n/u', '<br>', $content);
    
    // 转换转义字符：\* -> *
    $content = preg_replace('/\\\\([*_`~#\[\]()\\\\])/u', '$1', $content);
    
    return $content;
}

/**
 * Mistral API 相关配置
 */
$mistral_url = "https://api.mistral.ai/v1/chat/completions";

/**
 * 处理文本并通过 Mistral 进行改写
 *
 * @param string $prompt_text 原始文本
 * @param string $model_name 模型名称
 * @param bool $is_title 是否为标题改写
 * @return string 改写后的文本
 */
function mistral_rewrite($prompt_text, $model_name, $is_title = false)
{
    global $mistral_url;
    $api_key = "Suot3iK6LxkJXWsYBBUSfLCpM6u3U3Z8"; // 你的 Mistral API 密钥，请确保这里是你的真实密钥

    $full_prompt = "";
    $max_tokens_for_api = 1000; // 内容默认最大 tokens
    $rewritten_parts = []; // 用于存储改写后的文本块

    // --- MUST ADD: Specific handling for content rewriting (with image preservation) ---
    if (!$is_title) {
        // 1. 提取所有图片标签及其在原始文本中的位置
        // 使用 PREG_OFFSET_CAPTURE 获取匹配的起始偏移量
        preg_match_all('/(<img[^>]+>)/i', $prompt_text, $matches, PREG_OFFSET_CAPTURE | PREG_SET_ORDER);

        $last_pos = 0;
        $text_segments = []; // 用于存储文本片段
        $image_tags = [];    // 用于存储图片标签

        // 遍历所有图片匹配项，分割文本
        foreach ($matches as $match) {
            $img_tag = $match[0][0]; // 完整的 img 标签字符串
            $img_pos = $match[0][1]; // img 标签的起始位置

            // 提取图片前的文本片段
            $text_segment = substr($prompt_text, $last_pos, $img_pos - $last_pos);
            $text_segments[] = trim($text_segment); // 去除两端空白

            // 存储图片标签
            $image_tags[] = $img_tag;

            // 更新上一个处理位置
            $last_pos = $img_pos + strlen($img_tag);
        }
        // 添加最后一个文本片段（如果存在）
        $text_segments[] = trim(substr($prompt_text, $last_pos));

        // 2. 逐段发送文本给 AI 进行改写
        // 【修复1】：移除"歪嘎网讯："的要求，改为在最后统一添加
        $base_content_prompt = "请将以下文本内容进行中文改写，风格要像一个有血有肉的真人网站编辑撰写的原创文章。改写时请注意以下要求：

1. **情感表达**：根据内容主题，适当融入符合语境的情感色彩：
   - 有趣的内容可以用诙谐幽默的语调
   - 悲伤的事件可以用感慨惋惜的语调
   - 激动人心的消息可以用兴奋激动的语调
   - 争议性话题可以用理性但有态度的语调

2. **语言风格**：
   - 使用生动活泼的表达，避免过于严肃刻板的新闻格式
   - 适当使用感叹句、疑问句增强表现力
   - 可以用\"真是让人...\", \"不得不说...\", \"说实话...\", \"令人惊讶的是...\"等自然表达
   - 避免机器翻译般的生硬表达

3. **内容要求**：
   - 保持内容的准确性和真实性
   - 改写后的内容应有吸引力，自然流畅
   - **改写后的内容请务必分为清晰的段落**
   - 用词精准，符合中文表达习惯

请直接返回改写后的内容，不要包含任何解释性话语、前后缀或与内容无关的文字。

原文内容：\n\n";

        foreach ($text_segments as $index => $segment) {
            // 如果文本片段为空，则跳过改写，直接保留空字符串或占位符
            if (empty($segment)) {
                $rewritten_parts[] = ""; // 空文本段，AI不需要处理
                continue;
            }

            // 构建当前片段的完整 prompt
            $current_full_prompt = $base_content_prompt . $segment;

            $data = array(
                'model' => $model_name,
                'messages' => array(
                    array(
                        'role' => 'user',
                        'content' => $current_full_prompt
                    )
                ),
                'temperature' => 0.7,
                'max_tokens' => 2000 // 每个片段的 max_tokens，可以根据实际片段长度调整
            );

            // 初始化 cURL (每个片段都重新初始化，确保独立性)
            $ch = curl_init();
            curl_setopt($ch, CURLOPT_URL, $mistral_url);
            curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
            curl_setopt($ch, CURLOPT_POST, 1);
            curl_setopt($ch, CURLOPT_HTTPHEADER, array(
                'Content-Type: application/json',
                'Accept: application/json',
                'Authorization: Bearer ' . $api_key
            ));
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

            $result = curl_exec($ch);
            if (curl_errno($ch)) {
                // 如果某个片段请求失败，返回错误信息并中断
                curl_close($ch);
                return "Mistral 片段请求失败: " . curl_error($ch) . " (片段 " . $index . ")";
            }
            curl_close($ch);

            $response = json_decode($result, true);

            // 检查 API 返回的错误
            if (isset($response['object']) && $response['object'] === 'error') {
                return "API 返回错误：" . (isset($response['message']) ? $response['message'] : $result) . " (片段 " . $index . ")";
            }

            if (!isset($response['choices'][0]['message']['content'])) {
                return "API 返回错误：无法解析片段响应内容。原始响应：" . $result . " (片段 " . $index . ")";
            }

            // 【修复2】：清理AI返回内容中的无关文字
            $rewritten_content = trim($response['choices'][0]['message']['content']);
            
            // 移除可能的AI添加的解释性文字（常见的开头模式）
            $rewritten_content = preg_replace('/^(好的，|当然，|根据您的要求，|以下是改写后的内容：|改写后的内容如下：|这是改写后的内容：|改写结果：)/u', '', $rewritten_content);
            
            // 移除可能的AI添加的结尾总结
            $rewritten_content = preg_replace('/(以上就是改写后的内容。|改写完成。|希望这个改写符合您的要求。)$/u', '', $rewritten_content);
            
            // 移除多余的"歪嘎网讯："（防止AI在片段中添加）
            $rewritten_content = preg_replace('/歪嘎网讯：/u', '', $rewritten_content);
            
            // 【修复5】：将Markdown格式转换为HTML格式
            $rewritten_content = convertMarkdownToHTML($rewritten_content);
            
            $rewritten_parts[] = trim($rewritten_content);
        }

        // 3. 将改写后的文本片段和图片重新组合
        $final_rewritten_content = "";
        for ($i = 0; $i < count($text_segments); $i++) {
            $final_rewritten_content .= $rewritten_parts[$i]; // 添加改写后的文本片段
            if (isset($image_tags[$i])) {
                $final_rewritten_content .= $image_tags[$i]; // 添加图片标签
            }
        }

        // --- 必须添加：确保内容段落化 (将 \n\n 转换为 HTML 段落标签或 <br>) ---
        // 将 AI 生成的 `\n\n` 转换为 HTML 的 `<p>` 标签
        // 首先清理多余的换行，然后用 <p> 标签包裹
        $final_rewritten_content = preg_replace('/(\r?\n){3,}/', "\n\n", $final_rewritten_content); // 减少多余换行
        $final_rewritten_content = '<p>' . str_replace("\n\n", '</p><p>', $final_rewritten_content) . '</p>';
        $final_rewritten_content = str_replace('<p></p>', '', $final_rewritten_content); // 移除空段落标签

        // 【修复6】：智能添加"歪嘎网讯："前缀 - 确保它出现在第一段文字前，而不是图片前
        if (!empty($final_rewritten_content)) {
            // 检查是否已经有"歪嘎网讯："前缀，避免重复添加
            if (strpos($final_rewritten_content, '歪嘎网讯：') === false) {
                // 方法1：寻找第一个包含实际文字内容的<p>标签（忽略只有图片的<p>）
                if (preg_match('/^(.*?<p[^>]*>(?:<[^>]*>)*?)([^<\s][^<]*)/s', $final_rewritten_content, $matches)) {
                    // $matches[1] 是开头部分（包括图片、空白、<p>开始标签和其他HTML标签）
                    // $matches[2] 是第一段实际文字内容的开头
                    $final_rewritten_content = preg_replace(
                        '/^(.*?<p[^>]*>(?:<[^>]*>)*?)([^<\s][^<]*)/s',
                        '$1歪嘎网讯：$2',
                        $final_rewritten_content,
                        1 // 只替换第一次匹配
                    );
                } 
                // 方法2：如果没有<p>标签，寻找第一个非图片、非HTML标签的文字
                else if (preg_match('/^((?:<[^>]*>|\s)*?)([^<\s][^<]*)/s', $final_rewritten_content, $matches)) {
                    // $matches[1] 是开头的所有HTML标签（包括图片）和空白
                    // $matches[2] 是第一段文字内容
                    $final_rewritten_content = preg_replace(
                        '/^((?:<[^>]*>|\s)*?)([^<\s][^<]*)/s',
                        '$1歪嘎网讯：$2',
                        $final_rewritten_content,
                        1 // 只替换第一次匹配
                    );
                }
                // 方法3：如果都没匹配到，可能内容全是图片，则在内容开头添加一个段落
                else {
                    $final_rewritten_content = '<p>歪嘎网讯：</p>' . $final_rewritten_content;
                }
            }
        }
        return $final_rewritten_content;

    } else { // 这是处理标题的逻辑
        // 标题改写指令：明确要求字数限制
        $full_prompt = "请将以下文本改写为一个吸引人的中文标题，标题尽可能长点，描述清楚，但字数仍需要限制在60到70个字符之间，并直接返回改写后的标题，不要包含任何解释或前后缀。标题头尾除了原有内容自带符号否则不要加引号、星号（**）、书名号等任何原内容没有的符号。\n\n" . $prompt_text;
        $max_tokens_for_api = 200; // 标题的 max_tokens 设为较小值，30字中文通常不超过60 tokens

        $data = array(
            'model' => $model_name,
            'messages' => array(
                array(
                    'role' => 'user',
                    'content' => $full_prompt
                )
            ),
            'temperature' => 0.7,
            'max_tokens' => $max_tokens_for_api
        );

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $mistral_url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array(
            'Content-Type: application/json',
            'Accept: application/json',
            'Authorization: Bearer ' . $api_key
        ));
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            return "Mistral 请求失败: " . curl_error($ch);
        }
        curl_close($ch);

        $response = json_decode($result, true);

        if (isset($response['object']) && $response['object'] === 'error') {
            return "API 返回错误：" . (isset($response['message']) ? $response['message'] : $result);
        }

        if (!isset($response['choices'][0]['message']['content'])) {
            return "API 返回错误：无法解析响应内容。原始响应：" . $result;
        }

        // 【修复4】：清理标题中的无关文字
        $title_content = trim($response['choices'][0]['message']['content']);
        
        // 移除可能的AI添加的解释性文字
        $title_content = preg_replace('/^(好的，|当然，|根据您的要求，|以下是改写后的标题：|改写后的标题如下：|这是改写后的标题：|改写结果：)/u', '', $title_content);
        $title_content = preg_replace('/(以上就是改写后的标题。|改写完成。|希望这个标题符合您的要求。)$/u', '', $title_content);
        
        return trim($title_content);
    }
}

/**
 * 采集器数据处理逻辑
 */
if ($LabelArray['PageType'] == "List") {
    $LabelArray['Html'] = '当前页面的网址为:' . $LabelUrl . "\r\n页面类型为:" . $LabelArray['PageType'] . "\r\n接收到的数据是:" . $LabelArray['Html'];
} else if ($LabelArray['PageType'] == "Content") {
    $LabelArray['Html'] = '当前页面的网址为:' . $LabelUrl . "\r\n页面类型为:" . $LabelArray['PageType'] . "\r\n接收到的数据是:" . $LabelArray['Html'];
} else if ($LabelArray['PageType'] == "Save") {

    $model_name = $LabelArray['模型'];

    // 原创化标题
    if (!empty($LabelArray['标题'])) {
        $LabelArray['标题'] = mistral_rewrite($LabelArray['标题'], $model_name, true); // 标题改写
    } else {
        $LabelArray['标题'] = "标题为空，未进行改写。";
    }

    // 原创化内容
    // --- 必须修改：为内容改写调用 mistral_rewrite，传入 false ---
    if (!empty($LabelArray['内容'])) {
        $LabelArray['内容'] = mistral_rewrite($LabelArray['内容'], $model_name, false); // 内容改写
    } else {
        $LabelArray['内容'] = "内容为空，未进行改写。";
    }

    // 追加时间戳
    $LabelArray['时间'] = date('Y-m-d H:i:s', time());

    $LabelArray['模型'] = $LabelArray['模型'];
}

//############# 必须保留，保证采集器能正确获取数据 #############
echo serialize($LabelArray);
?>